export enum SprintingRaceType {
  Sprint = "Sprint",
  Hurdles = "Hurdles",
  MiddleDistance = "Middle Distance",
}

export const sprintingRaceTypes = Object.values(SprintingRaceType) as [
  SprintingRaceType,
  ...SprintingRaceType[],
];

export enum SprintTag {
  Start = "Start",
  Distance10m = "10m",
  Distance20m = "20m",
  Distance30m = "30m",
  Distance40m = "40m",
  Distance50m = "50m",
  Distance60m = "60m",
  Distance70m = "70m",
  Distance80m = "80m",
  Distance90m = "90m",
  Distance100m = "100m",
  FootContact = "Foot Contact",
  Touchdown = "Touchdown",
  ToeOff = "Toe Off",
  CustomDistance = "Custom Distance",
  StrideCount = "Stride Count",
}

export enum SprintingDistance {
  "100m" = "100m",
  "200m" = "200m",
  "400m" = "400m",
  "600m" = "600m",
  "100mH" = "100mH",
  "110mH" = "110mH",
  "400mH" = "400mH",
  "600mH" = "600mH",
  "800m" = "800m",
  "1500m" = "1500m",
  "3000m" = "3000m",
  "5000m" = "5000m",
}

export const sprintingDistances = Object.values(SprintingDistance) as [
  SprintingDistance,
  ...SprintingDistance[],
];

export const sprintingMiddleDistances = [
  SprintingDistance["800m"],
  SprintingDistance["1500m"],
  SprintingDistance["3000m"],
  SprintingDistance["5000m"],
];

export const sprintingHurdlesDistances = [
  SprintingDistance["100mH"],
  SprintingDistance["110mH"],
  SprintingDistance["400mH"],
  SprintingDistance["600mH"],
];

export const sprintingSprintDistances = [
  SprintingDistance["100m"],
  SprintingDistance["200m"],
  SprintingDistance["400m"],
  SprintingDistance["600m"],
];

export enum SprintingRound {
  Heat = "Heat",
  "Quarter-Final" = "Quarter-Final",
  "Semi-Final" = "Semi-Final",
  Final = "Final",
  "Time Trial" = "Time Trial",
  "Relay - Heat" = "Relay - Heat",
  "Relay - Final" = "Relay - Final",
}

export const sprintingRounds = Object.values(SprintingRound) as [
  SprintingRound,
  ...SprintingRound[],
];

export const sprintTags = {
  distances: [
    {
      value: SprintTag.Distance10m,
      key: "1",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: SprintTag.Distance20m,
      key: "2",
      className: "bg-fuchsia-20 text-fuchsia-k40",
    },
    {
      value: SprintTag.Distance30m,
      key: "3",
      className: "bg-orange-20 text-orange-k40",
    },
    {
      value: SprintTag.Distance40m,
      key: "4",
      className: "bg-purple-20 text-purple-k40",
    },
    {
      value: SprintTag.Distance50m,
      key: "5",
      className: "bg-yellow-20 text-yellow-k40",
    },
    {
      value: SprintTag.Distance60m,
      key: "6",
      className: "bg-red-20 text-red-k40",
    },
    {
      value: SprintTag.Distance70m,
      key: "7",
      className: "bg-green-20 text-green-k40",
    },
    {
      value: SprintTag.Distance80m,
      key: "8",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: SprintTag.Distance90m,
      key: "9",
      className: "bg-fuchsia-20 text-fuchsia-k40",
    },
    {
      value: SprintTag.Distance100m,
      key: "0",
      className: "bg-orange-20 text-orange-k40",
    },
  ],
  start: [
    {
      value: SprintTag.Start,
      key: "q",
      className: "bg-neonGreen-20 text-neonGreen-k40",
    },
  ],
  custom: [
    {
      value: SprintTag.CustomDistance,
      key: "t",
      className: "bg-green-20 text-green-k40",
    },
  ],
  movement: [
    {
      value: SprintTag.FootContact,
      key: "w",
      className: "bg-purple-20 text-purple-k40",
    },
    {
      value: SprintTag.Touchdown,
      key: "e",
      className: "bg-yellow-20 text-yellow-k40",
    },
    {
      value: SprintTag.ToeOff,
      key: "r",
      className: "bg-red-20 text-red-k40",
    },
  ],
};
