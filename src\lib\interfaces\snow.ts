export interface PTASnowFeature {
  id?: string;
  run_id: string;
  trick_num: number;
  trick?: {
    jump_takeoff_modifiers?: string;
    jump_direction?: string;
    jump_spin_type?: string;
    jump_spin_amount?: number;
    jump_spin_modifiers?: string;
    switch: boolean;
    grab_type?: string;
    cab: boolean;
    rail_in_spin_direction?: string;
    rail_in_spin_amount?: number;
    rail_in_spin_modifiers?: string;
    rail_in_grab?: string;
    rail_trick?: string;
    rail_on_spin_direction?: string;
    rail_on_spin_amount?: number;
    rail_on_spin_modifiers?: string;
    rail_on_grab?: string;
    rail_out_spin_direction?: string;
    rail_out_spin_amount?: number;
    rail_out_spin_modifiers?: string;
    rail_out_grab?: string;
  };
  fis_trick_id?: string;
  feature_num: number;
  feature_type?: string;
  section_type?: string;
  feature_description?: string;
  landing_zone?: string;
  landing_type?: string;
  landing_description?: string;
  score?: number;
  video_id?: string;
  start_frame?: number;
  end_frame?: number;
  take_off_frame?: number;
  landing_frame?: number;
  air_time?: number;
  grab_time?: number;
  progression?: boolean;
  trick_execution?: string;
}
