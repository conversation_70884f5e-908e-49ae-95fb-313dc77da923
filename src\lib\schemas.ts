import { z } from "zod";
import { LandingDescription, SnowRailSpinDirection } from "./enums/snow";
import {
  SwimmingStrokeCategory,
  SwimCourseType,
  SwimmingSource,
  SwimmingStartType,
} from "./enums/swimming";
import { Gender } from "./enums/enums";
import { HighJumpType } from "./enums/highJump";
import { HighJumpSide } from "./enums/highJump";
import { BoxingCorner, BoxingPunchType } from "./enums/boxing";
import { DiscusHand, DiscusMovement, DiscusType } from "./enums/discus";
import {
  SprintingDistance,
  SprintingRound,
  SprintingRaceType,
} from "./enums/sprint";

export const swimmingRaceFormSchema = z.object({
  course_type: z.nativeEnum(SwimCourseType),
  date: z.date(),
  round: z
    .string()
    .regex(
      /^(Heat|Semi Final)( - [1-9]| - 1[0-9])$|^(Final)(| - [A|B|C|D])$|^Swim Off$/,
      {
        message:
          "Invalid round format. Valid examples: 'Heat - 1', 'Semi Final - 1', 'Final - A', 'Swim Off'",
      },
    ),
  stroke_category: z.nativeEnum(SwimmingStrokeCategory),
  race_distance: z.string(),
  gender: z.nativeEnum(Gender),
  is_relay: z.boolean(),
  age_category: z.string().nullable(),
  classification: z.string().nullable(),
});

export const swimmingSessionFormSchema = z.object({
  race_id: z.string().nullish(),
  date: z.date(),
  session_type: z.string(),
  session_description: z.string(),
  course_type: z.nativeEnum(SwimCourseType),
  placing: z.string().nullable(),
  start_time: z.string().nullable(),
  official_time: z.string().nullable(),
  result_code: z.string().nullable(),
  is_relay: z.boolean(),
  athleteId: z.string().nullish(),
  relay: z.array(z.string()).nullish(),
});

export const swimmingRepFormSchema = z.object({
  id: z.string().optional(),
  video_id: z.string(),
  session_id: z.string(),
  athlete_id: z.string(),
  athlete_name: z.string(),
  stroke_category: z.nativeEnum(SwimmingStrokeCategory),
  start_type: z.nativeEnum(SwimmingStartType),
  source: z.nativeEnum(SwimmingSource),
  race_suit: z.boolean(),
  piece_number: z.string(),
  set_number: z.string(),
  rep_number: z.string(),
  duration: z.string(),
  distance: z.string(),
  speed: z.string(),
  reaction_time: z.string().nullish(),
});

export const updateRaceAthleteSchema = z.object({
  // id: z.string().optional(),
  athleteId: z.string(),
  videoId: z.string(),
  round: z.string().optional(),
  placing: z.string().optional(),
  time: z.string().optional(),
  lane: z.string().optional(),
  reactionTime: z.string().optional(),
  isValid: z.boolean().optional(),
  splits: z
    .array(
      z.object({
        id: z.string().optional(),
        distance: z.number(),
        time: z.string(),
      }),
    )
    .optional(),
});

export const snowFormSchema = z.object({
  id: z.string().optional(),
  startFrame: z.string(),
  endFrame: z.string(),
  trickNum: z.string().nullable(), //halfpipe , hit number; slope style section Num
  amplitude: z.string().nullable(), //halfpipe only
  score: z.string().nullish(), //big air & slope style only
  jumpTypeId: z.string().nullable(),
  jumpTakeoffModifierId: z.string().nullable(),
  progression: z.string(),
  switch: z.string(),
  cab: z.string(),
  spinDirection: z.string().nullable(),
  spinTypeId: z.string().nullable(),
  spinAmount: z.string().nullable(),
  spinModifierId: z.string().nullable(),
  grabTypeId: z.string().nullable(),
  grabStart: z.string().nullable(),
  grabEnd: z.string().nullable(),
  takeOffFrame: z.string().nullable(),
  landingFrame: z.string().nullable(),
  executionId: z.string().nullable(),
  landingZone: z.string().nullable(),
  landingType: z.string().nullable(),
  landingDescriptions: z.array(z.nativeEnum(LandingDescription)).nullable(),

  //rail fields
  railFeatureId: z.string().nullish(),

  railInSpinDirection: z.nativeEnum(SnowRailSpinDirection).nullish(),
  railInSpinAmount: z.string().nullish(),
  railInSpinModifierId: z.string().nullish(),
  railInGrabId: z.string().nullish(),

  railTrickId: z.string().nullish(),
  railOnSpinDirection: z.nativeEnum(SnowRailSpinDirection),
  railOnSpinAmount: z.string().nullish(),
  railOnSpinModifierId: z.string().nullish(),
  railOnGrabId: z.string().nullish(),

  railOutSpinDirection: z.nativeEnum(SnowRailSpinDirection),
  railOutSpinAmount: z.string().nullish(),
  railOutSpinModifierId: z.string().nullish(),
  railOutGrabId: z.string().nullish(),

  //transition
  transitionTypeId: z.string().nullish(),
});

export const highJumpSchema = z.object({
  id: z.string(),
  number: z.string(),
  approachSide: z.nativeEnum(HighJumpSide),
  height: z.string(),
  success: z.string(),
  type: z.nativeEnum(HighJumpType),
  equipment: z.array(z.enum(["bar", "box"])),
  comment: z.string(),
  startFrame: z.string(),
  endFrame: z.string(),
});

//if not success then fail
//if not head then body
export const boxingPunchSchema = z.object({
  id: z.string(),
  number: z.string(),
  round: z.string(),
  punch: z.nativeEnum(BoxingPunchType),
  success: z.enum(["true", "false"]),
  head: z.enum(["true", "false"]),
  unsure: z.enum(["true", "false"]),
  feint: z.enum(["true", "false"]),
  clinch: z.enum(["true", "false"]),
  switch: z.enum(["true", "false"]),
  color: z.nativeEnum(BoxingCorner),
});

export const discusThrowSchema = z.object({
  id: z.string().optional(),
  number: z.string(),
  type: z.nativeEnum(DiscusType),
  movement: z.nativeEnum(DiscusMovement),
  hand: z.nativeEnum(DiscusHand),
  nonReverse: z.enum(["true", "false"]),
  weight: z.string(),
  denfyTool: z.enum(["true", "false"]),
  startFrame: z.string().optional(),
  endFrame: z.string().optional(),
});

export const sprintRaceSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  eventGroup: z.nativeEnum(SprintingRaceType),
  distance: z.nativeEnum(SprintingDistance),
  round: z.nativeEnum(SprintingRound),
  position: z.string(),
  time: z.string().min(1),
  track: z.string().min(1),
  gender: z.nativeEnum(Gender),
  date: z.string().min(1),
  wind: z.string(),
  strideFreq: z.string(),
});

export const sprintTagSchema = z.object({
  id: z.string().optional(),
  sprintingRaceId: z.string(),
  athleteId: z.string().optional(),
  tag: z.string(),
  frame: z.string(),
});
