{"openapi": "3.0.0", "info": {"title": "Swagger API DOC", "version": "1.0"}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "paths": {"/api/v1/shotput/{id}/process": {"post": {"description": "Add shotput tags to video.", "tags": ["Shotput"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid input data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid request body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["throws"], "properties": {"throws": {"type": "array", "items": {"type": "object", "required": ["athleteId", "number", "movement", "type", "hand", "tags"], "properties": {"athleteId": {"type": "string"}, "number": {"type": "number"}, "movement": {"type": "string", "enum": ["ROTATIONAL", "GLIDE"]}, "type": {"type": "string", "enum": ["TRAINING", "COMPETITION"]}, "hand": {"type": "string", "enum": ["RIGHT", "LEFT"]}, "tags": {"type": "array", "items": {"type": "object", "required": ["phase", "frame"], "properties": {"phase": {"type": "string", "enum": ["START", "ENTRY", "FLIGHT", "DELIVERY", "RELEASE", "RECOVERY"]}, "frame": {"type": "number"}}}}}}}}}}}}}}, "/api/v1/shotput/{id}": {"get": {"description": "Get shotput throws for a video.", "tags": ["Shotput"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "athleteId": {"type": "string"}, "number": {"type": "number"}, "movement": {"type": "string", "enum": ["ROTATIONAL", "GLIDE"]}, "type": {"type": "string", "enum": ["TRAINING", "COMPETITION"]}, "hand": {"type": "string", "enum": ["RIGHT", "LEFT"]}, "phases": {"type": "array", "items": {"type": "object", "properties": {"phase": {"type": "string", "enum": ["START", "ENTRY", "FLIGHT", "DELIVERY", "RELEASE", "RECOVERY"]}, "frame": {"type": "number"}}}}}}}}}}}}, "400": {"description": "Bad Request - Invalid video ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid video id"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}, "/api/v1/swimming/{id}/process": {"post": {"description": "Add swimming tags to video.", "tags": ["Swimming"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid input data", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid request body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["athleteId", "frame", "tag"], "properties": {"athleteId": {"type": "string", "description": "The ID of the athlete"}, "frame": {"type": "number", "description": "The frame number in the video"}, "tag": {"type": "string", "description": "The swimming tag type"}, "x1": {"type": "number", "nullable": true, "description": "X coordinate of the first point"}, "x2": {"type": "number", "nullable": true, "description": "X coordinate of the second point"}, "y1": {"type": "number", "nullable": true, "description": "Y coordinate of the first point"}, "y2": {"type": "number", "nullable": true, "description": "Y coordinate of the second point"}}}}}}}}}, "/api/v1/swimming/{id}": {"get": {"description": "Get swimming tags for a video.", "tags": ["Swimming"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}, "frame": {"type": "number"}, "tag": {"type": "string"}, "x1": {"type": "number", "nullable": true}, "x2": {"type": "number", "nullable": true}, "y1": {"type": "number", "nullable": true}, "y2": {"type": "number", "nullable": true}}}}}}}, "400": {"description": "Bad Request - Invalid video ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid video id"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}}, "tags": []}