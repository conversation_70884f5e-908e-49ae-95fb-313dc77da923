"use client";

import { useCallback, useEffect, useRef, useState } from "react";
export interface ZoomState {
  scale: number;
  offsetX: number;
  offsetY: number;
}

export interface VideoZoomProps {
  containerRef: React.RefObject<HTMLDivElement>;
  onZoomChange?: (zoomState: ZoomState) => void;
}

export interface VideoZoomHandlers {
  handleWheel: (e: React.WheelEvent) => void;
  handleMouseDown: (e: React.MouseEvent) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  handleMouseUp: () => void;
  handleDoubleClick: (e: React.MouseEvent) => void;
  resetZoom: () => void;
  zoomState: ZoomState;
  getTransformStyle: () => React.CSSProperties;
}

export function useVideoZoom({
  containerRef,
  onZoomChange,
}: VideoZoomProps): VideoZoomHandlers {
  const [zoomState, setZoomState] = useState<ZoomState>({
    scale: 1,
    offsetX: 0,
    offsetY: 0,
  });

  const [isPanning, setIsPanning] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const isCtrlPressed = useRef(false);

  // Prevent default zoom behavior (we want to do this becasue of trackpads since hpsnz mostly use laptops)
  useEffect(() => {
    const preventDefaultZoom = (e: WheelEvent) => {
      if (Math.abs(e.deltaY) < 100 && e.ctrlKey) {
        e.preventDefault();
      }
    };

    // Add event listener to the container
    const container = containerRef.current;
    if (container) {
      container.addEventListener("wheel", preventDefaultZoom, {
        passive: false,
      });
    }

    return () => {
      if (container) {
        container.removeEventListener("wheel", preventDefaultZoom);
      }
    };
  }, [containerRef]);

  // Track Ctrl key for preventing conflicts with drawing
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        isCtrlPressed.current = true;
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        isCtrlPressed.current = false;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  // Notify parent of zoom changes
  useEffect(() => {
    onZoomChange?.(zoomState);
  }, [zoomState, onZoomChange]);

  const resetZoom = useCallback(() => {
    setZoomState({
      scale: 1,
      offsetX: 0,
      offsetY: 0,
    });
  }, []);

  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();

      // Don't zoom if Ctrl is pressed (reserved for drawing)
      if (isCtrlPressed.current) return;

      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      // Calculate zoom factor
      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;

      setZoomState((prev) => {
        // Constrain zoom: 1.0 = natural size (minimum), 5.0 = maximum zoom
        const newScale = Math.max(1.0, Math.min(5, prev.scale * zoomFactor));

        // If we're at natural size (1.0), reset pan offset to center
        if (newScale === 1.0) {
          return {
            scale: newScale,
            offsetX: 0,
            offsetY: 0,
          };
        }

        // Calculate new offset to zoom towards mouse position
        const scaleChange = newScale / prev.scale;
        const newOffsetX = mouseX - (mouseX - prev.offsetX) * scaleChange;
        const newOffsetY = mouseY - (mouseY - prev.offsetY) * scaleChange;

        return {
          scale: newScale,
          offsetX: newOffsetX,
          offsetY: newOffsetY,
        };
      });
    },
    [containerRef],
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      // Only start panning if not drawing (Ctrl not pressed) and zoomed in beyond natural size
      if (!isCtrlPressed.current && zoomState.scale > 1.0) {
        setIsPanning(true);
        setLastMousePos({ x: e.clientX, y: e.clientY });
        e.preventDefault();
      }
    },
    [zoomState.scale],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning && !isCtrlPressed.current) {
        const deltaX = e.clientX - lastMousePos.x;
        const deltaY = e.clientY - lastMousePos.y;

        setZoomState((prev) => ({
          ...prev,
          offsetX: prev.offsetX + deltaX,
          offsetY: prev.offsetY + deltaY,
        }));

        setLastMousePos({ x: e.clientX, y: e.clientY });
        e.preventDefault();
      }
    },
    [isPanning, lastMousePos],
  );

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  const handleDoubleClick = useCallback(
    (e: React.MouseEvent) => {
      // Don't reset zoom if Ctrl is pressed
      if (!isCtrlPressed.current) {
        resetZoom();
        e.preventDefault();
      }
    },
    [resetZoom],
  );

  const getTransformStyle = useCallback((): React.CSSProperties => {
    return {
      transform: `translate(${zoomState.offsetX}px, ${zoomState.offsetY}px) scale(${zoomState.scale})`,
      transformOrigin: "0 0",
    };
  }, [zoomState]);

  return {
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleDoubleClick,
    resetZoom,
    zoomState,
    getTransformStyle,
  };
}
