"use client";

import type React from "react";

import { Fragment, useEffect, useRef, useState } from "react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuTrigger,
} from "~/components/ui/context-menu";
import { <PERSON>rollArea, ScrollBar } from "~/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";
import type { TagUI } from "~/lib/interface";
import { checkIsAiTag, cn, getSport } from "~/lib/utils";
import { Card } from "../Card";
import {
  HighJumpTagDeleteMenuItem,
  ShotputTagDeleteMenuItem,
  SwimmingTagDeleteMenuItem,
} from "./TagDeleteMenuItem";

// Helper function to check if a tag is a custom distance (numeric value)
const isCustomDistance = (tag: string): boolean => {
  return !isNaN(Number(tag));
};

export const VideoTimeline = ({
  tagTypes,
  totalFrames,
  onFrameNumberChange,
  setEditingTagId,
}: {
  tagTypes: TagUI[];
  totalFrames: number;
  onFrameNumberChange: (frame: string) => void;
  setEditingTagId: (tagId: string | null) => void;
}) => {
  const pointerRef = useRef<HTMLDivElement>(null);
  const videoSummary = useStore((state) => state.videoSummary);
  const filteredTags = useStore((state) => state.filteredTags);
  const currentFrame = useStore((state) => state.currentFrame);
  const selectedHighJump = useStore((state) => state.selectedHighJump);
  const shotputThrow = useStore((state) => state.shotputThrow);

  const sport = getSport(videoSummary);

  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  useEffect(() => {
    if (pointerRef.current) {
      pointerRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  }, [currentFrame]);

  useEffect(() => {
    setSelectedTags([]);
  }, [filteredTags]);

  const onTimelineClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = event.currentTarget;
    const clickPosition =
      event.clientX - progressBar.getBoundingClientRect().left;
    onFrameNumberChange(Math.round(clickPosition).toString());
  };

  const cursorHeight = (tagTypes.length + 1) * 18;
  const strideCount = selectedHighJump?.strides.map((s) => s.number);
  const getEquipment = (jump: typeof selectedHighJump) => {
    if (!jump) return "none";
    if (jump.withBar && jump.withBox) return "Bar, Box";
    if (jump.withBar) return "bar";
    if (jump.withBox) return "box";

    return "";
  };

  const jumpInfoItems = [
    {
      label: "Stride Count",
      value: strideCount,
    },
    {
      label: "Approach Side",
      value: selectedHighJump?.approachSide,
    },
    {
      label: "Jump Type",
      value: selectedHighJump?.type,
    },
    {
      label: "Equipment",
      value: getEquipment(selectedHighJump),
    },
    {
      label: "Success",
      value: !selectedHighJump
        ? "N/A"
        : selectedHighJump.success
          ? "Yes"
          : "No",
    },
  ] as const;

  const formattedTotalFrames = Math.ceil(totalFrames ?? 1);

  return (
    <ScrollArea className="h-[166px] w-full select-none whitespace-nowrap rounded-lg border bg-white px-2.5 pt-[5px] text-smallLabel">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger
            asChild
            className="focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          >
            <div className="flex w-max flex-col">
              <div className="sticky top-0 z-40 flex gap-2 border-b bg-white">
                <p className="sticky left-0 top-0 z-50 w-[101px] border-r bg-white py-[5px]">
                  Frame ({formattedTotalFrames.toFixed(0)})
                </p>
                <div
                  className="relative cursor-pointer"
                  style={{ width: formattedTotalFrames }}
                  onClick={onTimelineClick}
                >
                  {Array.from({ length: formattedTotalFrames }, (_, i) => {
                    if (i % 50 === 0) {
                      return (
                        <Fragment key={i}>
                          <p
                            className="absolute bottom-[50%] translate-x-[-50%] translate-y-[50%]"
                            style={{ left: i }}
                          >
                            {i % 50 === 0 ? i : ""}
                          </p>
                          <div
                            className="absolute top-5 h-[146px] translate-x-[-50%] border-r border-dashed border-[#E9E9E9]"
                            style={{ left: i }}
                          />
                        </Fragment>
                      );
                    }
                  })}
                  <div
                    ref={pointerRef}
                    className="absolute top-[-5px] w-[1px] translate-x-[-50%] rounded-full border border-black/30 bg-seaSalt-k40"
                    style={{
                      height: cursorHeight < 166 ? 166 : cursorHeight,
                      left: currentFrame,
                    }}
                  />
                </div>
              </div>
              <div className="min-h-[166px]">
                <div className="absolute left-[110px] top-0 h-[166px] w-[1px] bg-black/10" />
                {tagTypes.map((tagType) => {
                  if (!tagType) return null;
                  const typeTags = filteredTags.filter((x) => {
                    // Handle numeric tags (custom distances)
                    if (
                      tagType.value === "Custom Distance" &&
                      isCustomDistance(x.tag)
                    ) {
                      return true;
                    }

                    if (x.tag === tagType.value) {
                      return true;
                    }
                    if (
                      ["5m", "15m", "25m", "35m", "45m"].includes(x.tag) &&
                      tagType.key === "distance"
                    ) {
                      return true;
                    }
                    return false;
                  });
                  return (
                    <div key={tagType.value} className="flex">
                      <div className="sticky left-0 z-20 h-full w-[101px] border-r py-[1.5px]">
                        <p
                          className={cn(
                            "w-fit rounded-[2.5px] px-[5px] py-[2.5px] lowercase",
                            tagType.className,
                          )}
                        >
                          {tagType.label ?? tagType.value}
                        </p>
                      </div>
                      <div className="relative">
                        {typeTags.map((tag) => {
                          if (tag.frame < 1 || tag.isDeleted) return null;
                          const isAITag = checkIsAiTag(tag.userId);

                          // For numeric tags (custom distances), use a more descriptive title
                          const tagTitle = isCustomDistance(tag.tag)
                            ? `Distance: ${tag.tag}m at frame ${tag.frame}`
                            : tag.frame.toString();

                          return (
                            <ContextMenu key={tag.id}>
                              <ContextMenuTrigger asChild>
                                <div
                                  title={tagTitle}
                                  className={cn(
                                    "absolute top-[50%] z-0 h-4 w-4 translate-y-[-50%] rounded-[2.5px] border hover:ring-1 hover:ring-black/60 hover:ring-offset-2",
                                    isAITag
                                      ? "border-black/10"
                                      : "rounded-full border-black/60",
                                    tagType.className,
                                    (tag.frame === currentFrame ||
                                      selectedTags.includes(tag.id)) &&
                                      "ring-2 ring-gray-500 ring-offset-2",
                                  )}
                                  style={{ left: tag.frame }}
                                  onClick={(e) => {
                                    onFrameNumberChange(tag.frame.toString());
                                    setEditingTagId(tag.id);
                                    // if ctrl is down, add to selectedStrides
                                    if (e.ctrlKey) {
                                      setSelectedTags((prev) => [
                                        ...prev,
                                        tag.id,
                                      ]);
                                    } else {
                                      setSelectedTags([tag.id]);
                                    }
                                  }}
                                  onMouseDown={(e) => e.preventDefault()}
                                  tabIndex={-1}
                                />
                              </ContextMenuTrigger>
                              <ContextMenuContent>
                                {selectedHighJump && (
                                  <HighJumpTagDeleteMenuItem
                                    id={tag.id}
                                    selectedTags={selectedTags}
                                  />
                                )}
                                {shotputThrow && (
                                  <ShotputTagDeleteMenuItem
                                    id={tag.id}
                                    selectedTags={selectedTags}
                                  />
                                )}
                                {sport === Sport.swimming && (
                                  <SwimmingTagDeleteMenuItem
                                    id={tag.id}
                                    selectedTags={selectedTags}
                                  />
                                )}
                              </ContextMenuContent>
                            </ContextMenu>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </TooltipTrigger>

          <TooltipContent side="top" className="relative">
            {selectedHighJump && (
              <Card className="flex w-[192px] flex-col items-center justify-center gap-[5px]">
                <div className="flex w-full items-start gap-[5px]">
                  <p className="flex-1 text-smallLabel font-bold">
                    Jump {selectedHighJump?.number}
                  </p>
                  <p className="flex-1 text-right text-smallLabel font-bold">
                    {selectedHighJump?.height}m
                  </p>
                </div>
                <hr className="h-[1px] w-[172px] outline-neutral-800" />
                {jumpInfoItems.map((item) => (
                  <div
                    key={item.label}
                    className="flex w-full items-start gap-[5px]"
                  >
                    <p className="flex-1 text-smallLabel text-gray">
                      {item.label}
                    </p>
                    <p className="flex-1 text-right text-label">{item.value}</p>
                  </div>
                ))}
              </Card>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  );
};
