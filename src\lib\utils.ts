import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import type { GetVideoInfoOutput } from "~/server/api/routers/video";
import type { ShotputThrows } from "~/server/db/schema";
import type { SnowFeature } from "~/server/db/snowSchema";
import { transitionSectionMap } from "./constants";
import { Sport, sports, type UserRole } from "./enums/enums";
import { DiscusTag, discusTagsUI } from "./enums/discus";
import type { SnowFeatureType } from "./enums/snow";
import { SwimCourseType } from "./enums/swimming";
import type { AllSport, TaglistTag, TagUI } from "./interface";
import { swimmingTags } from "./enums/swimming";
import { highJumpTags } from "./enums/highJump";
import { snowTimelineTags } from "./enums/snow";
import type { DiscusThrows } from "~/server/db/discusSchema";
import { ShotputTag, shotputTags } from "~/lib/enums/shotput";
import { sprintTags } from "./enums/sprint";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getSwimmingSplits = ({
  metadata,
  courseType,
}: {
  metadata: { text: string }[];
  courseType?: SwimCourseType | null;
}) => {
  if (!courseType) return [];
  const regex = /^\d+m$/;
  let length = 0;
  metadata.forEach((tag) => {
    if (regex.test(tag.text)) {
      length = parseInt(tag.text.replace("m", ""));
    }
  });

  const splitLength = courseType === SwimCourseType.long ? 50 : 25;
  const splits = [];
  for (let i = splitLength; i <= length; i += splitLength) {
    splits.push(i);
  }
  return splits;
};

export function isNumberString(value?: string): boolean {
  if (!value) return false;
  return !isNaN(Number(value));
}

export const getPrevTag = (
  currentFrame: number,
  tags: TaglistTag[],
  editingTagId?: string | null,
) => {
  if (!tags) return undefined;

  const editingTag = tags.find((x) => x.id === editingTagId);

  if (!editingTag || editingTag.frame !== currentFrame) {
    //go to previous tag by frame number
    const prevTags = tags
      .filter((x) => x.frame < currentFrame)
      .sort((a, b) => b.frame - a.frame);
    const prevTag = prevTags[0];
    return prevTag;
  }
  const editingTagIndex = tags.findIndex((x) => x.id === editingTagId);
  return tags[editingTagIndex - 1];
};

export const getNextTag = (
  currentFrame: number,
  tags: TaglistTag[],
  editingTagId?: string | null,
) => {
  if (!tags) return undefined;
  const editingTag = tags.find((x) => x.id === editingTagId);

  if (!editingTag || editingTag.frame !== currentFrame) {
    //go to next tag by frame number
    const nextTags = tags
      .filter((x) => x.frame > currentFrame)
      .sort((a, b) => a.frame - b.frame);
    const nextTag = nextTags[0];
    return nextTag;
  }
  const editingTagIndex = tags.findIndex((x) => x.id === editingTagId);
  return tags[editingTagIndex + 1];
};

export const getSport = (videoSummary: GetVideoInfoOutput | null) => {
  if (!videoSummary) return undefined;
  switch (videoSummary.sport) {
    case Sport.athletics:
      const metadatas = videoSummary.tags;
      const isShotput = metadatas?.some((x) => x.text === "Shotput");
      if (isShotput) return "shotput";
      const isDiscus = metadatas?.some((x) => x.text === "Discus");
      if (isDiscus) return "discus";
      const isHighjump = !!metadatas?.some((x) => x.text === "High Jump");
      if (isHighjump) return "highJump";
      const isSprinting = !!metadatas?.some((x) => x.text === "Sprint");
      if (isSprinting) return "sprinting";
      return videoSummary.sport;
    default:
      return videoSummary.sport;
  }
};

export const getFilteredTags = ({
  sport,
  athleteId,
  tags,
  tagTypes,
  filterValues,
  sortByAiConfidence,
}: {
  sport?: AllSport;
  athleteId?: string;
  tags: TaglistTag[];
  tagTypes: Record<string, TagUI[]>;
  filterValues: string[];
  sortByAiConfidence?: null | "asc" | "desc";
}) => {
  if (!athleteId || !sport) return [];
  let result = [...tags]
    .filter((x) => x.athleteId === athleteId)
    .sort((a, b) => {
      if (sortByAiConfidence === "asc") {
        return (a.aiConfidence ?? 0) - (b.aiConfidence ?? 0);
      } else if (sortByAiConfidence === "desc") {
        return (b.aiConfidence ?? 0) - (a.aiConfidence ?? 0);
      }
      return a.frame - b.frame;
    });

  //filter by tag
  if (filterValues.length > 0) {
    result = result.filter((x) => {
      // Handle numeric tags (custom distances)
      if (isNumberString(x.tag) && filterValues.includes("Custom Distance")) {
        return true;
      }

      if (filterValues.includes(x.tag)) return true;
      const keyword = Object.values(tagTypes)
        .flat()
        .find((t) => t?.value === x.tag)?.keyword;
      if (keyword && filterValues.includes(keyword)) return true;
      return false;
    });
  }

  switch (sport) {
    case Sport.swimming:
      const inactiveAITagIds = result
        .filter((x) => !!x.aiTagId)
        .map((x) => x.aiTagId);

      result = result.filter(
        (x) => !inactiveAITagIds.includes(x.id) && !x.isDeleted,
      );

      break;
    case "shotput":
      result = result.filter((x) => !x.isDeleted);
      break;
    case "discus":
      result = result.filter((x) => !x.isDeleted);
      break;
    case "sprinting":
      result = result.filter((x) => !x.isDeleted);

      // For display purposes, add a displayTag property for numeric custom distance tags
      result = result.map((tag) => {
        if (isNumberString(tag.tag)) {
          return {
            ...tag,
            displayTag: `${tag.tag}m`, // Add a displayTag property for UI
          };
        }
        return tag;
      });
      break;
    default:
      result = result.filter((x) => !x.isDeleted);
      break;
  }

  return result;
};

export const checkIsAiTag = (userId?: string) => {
  if (!userId) return false;
  return (
    userId === process.env.NEXT_PUBLIC_SERVICE_USER_ID ||
    (userId?.length ?? 0) < 5
  );
};

export const getUserSports = (roles: UserRole[]) => {
  const roleSports: Sport[] = [];
  roles.forEach((role) => {
    if (role.includes("sport")) {
      let sport = role.split("_")[1];
      if (sport === "snowsports") {
        sport = "snow_sports";
      }
      if (sports.includes(sport as Sport)) {
        roleSports.push(sport as Sport);
      }
    }
  });

  return roleSports.sort((a, b) => a.localeCompare(b));
};

export const formatString = (value: string) => value.replace(/_/g, " ");

export const getAthleteOptions = (
  videoSummary: GetVideoInfoOutput | null,
  throws?: (ShotputThrows | DiscusThrows)[],
) => {
  if (!videoSummary) return [];
  switch (getSport(videoSummary)) {
    case "shotput":
      const result = videoSummary.athletes ?? [];
      throws?.forEach((throwData) => {
        const athlete = result.find((x) => x.athleteId === throwData.athleteId);
        if (!athlete) {
          result.push({
            athleteId: throwData.athleteId,
            name: throwData.athleteId,
          });
        }
      });
      return result;
    default:
      return videoSummary.athletes ?? [];
  }
};

export const getDefaultSelectedAthlete = (videoSummary: GetVideoInfoOutput) => {
  switch (getSport(videoSummary)) {
    case Sport.swimming:
    case "shotput":
    case Sport.athletics:
      return videoSummary.athletes?.[0]?.athleteId;
    default:
      return undefined;
  }
};

export const getTimelineTagTypes = (
  videoSummary: GetVideoInfoOutput,
): TagUI[] => {
  switch (getSport(videoSummary)) {
    case Sport.swimming:
      return [
        { value: "distance", key: "distance", className: "bg-seaSalt-40" },
        ...swimmingTags.movement,
      ];
    case "shotput":
      return shotputTags["key phase"];
    case "discus":
      return discusTagsUI["key phase"];
    case "highJump":
      return [...highJumpTags.stride, ...highJumpTags.jump];
    case Sport.snowsports:
      return snowTimelineTags;
    case "sprinting":
      return [
        ...sprintTags.distances,
        ...sprintTags.movement,
        ...sprintTags.custom,
      ];
    default:
      return [];
  }
};

export const getStringNumber = (value: string | null) => {
  if (!value) return null;
  if (isNaN(Number(value))) return null;
  return Number(value);
};

export const getStringBoolean = (value: string | null) => {
  return value === "1";
};

export const getSnowSectionWord = (
  sectionNum: number,
  feature?: SnowFeature,
) => {
  let sectionFeature: SnowFeatureType | null = null;
  if (feature?.sections) {
    const sectionLetter = feature.sections.charAt(sectionNum - 1).toLowerCase();
    sectionFeature =
      transitionSectionMap[sectionLetter as keyof typeof transitionSectionMap];
  }
  return sectionFeature;
};

const rightHandMap = {
  shotput: {
    [ShotputTag.RightToeOff]: "Left Toe Off",
    [ShotputTag.LeftToeOffBack]: "Right Toe Off Back",
    [ShotputTag.RightFootDown]: "Left Foot Down",
  },
  discus: {
    [DiscusTag.RightToeOff]: "Left Toe Off",
    [DiscusTag.LeftFootDown]: "Right Foot Down",
    [DiscusTag.RightFootDown]: "Left Foot Down",
  },
};

export const getTagDisplay = ({
  label,
  leftHand,
  sport,
}: {
  label: string;
  leftHand: boolean;
  sport?: AllSport;
}) => {
  if (!sport || !["shotput", "discus"].includes(sport)) return label;
  if (leftHand) return label;
  const map = rightHandMap[sport as keyof typeof rightHandMap];
  return map[label as keyof typeof map] ?? label;
};
