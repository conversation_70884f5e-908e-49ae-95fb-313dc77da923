"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useStore } from "~/hooks/store";
import { SnowDiscipline, SnowEvent, SnowRunSource } from "~/lib/enums/snow";
import type { SnowsportRace } from "~/lib/interfaces/externalCall";
import { api } from "~/trpc/react";
import { Card } from "../Card";
import { Selector } from "../Selector";
import { Input } from "../ui/input";
import { EditSlopeFeature } from "./EditSlopeFeature";
import { EditableSelector } from "../Selector";
import { Gender } from "~/lib/enums/enums";
import toast from "react-hot-toast";
import { AthleteSelect } from "~/app/validation/components/AthleteSelect";

export const SnowEditRace = ({
  race,
  athleteOptions,
}: {
  race?: SnowsportRace;
  athleteOptions: { athleteId: string; name: string }[];
}) => {
  const params = useParams();
  const router = useRouter();
  const videoId = params.id as string;
  const videoSummary = useStore((state) => state.videoSummary);

  const initialValues = {
    event_id: "new",
    competition_id: videoSummary?.competition?.id ?? "",
    round: "",
    date: "",
    gender: Gender.Men,
    event: SnowEvent.SLOPESTYLE,
    discipline: SnowDiscipline.FREESKI,
    age_category: "",
    analysis_pdf: "",
    source: SnowRunSource.VIDEO_ANALYSIS,
    results: [],
  };
  const [selectedAthlete, setSelectedAthlete] = useState(
    race?.results[0]?.athlete_id,
  );
  const [selectedRace, setSelectedRace] = useState(race ?? initialValues);

  useEffect(() => {
    if (race) {
      setSelectedRace(race);
    } else {
      setSelectedRace(initialValues);
    }
  }, [race]);

  const { data: races } = api.snow.getRacesByCompetitionId.useQuery(
    {
      competitionId: videoSummary?.competition?.id ?? "",
    },
    {
      enabled: !!videoSummary?.competition?.id,
    },
  );

  const { mutate: upsertRace, isPending: isPendingUpdateVideo } =
    api.snow.upsertRace.useMutation({
      onSuccess: () => {
        router.refresh();
        toast.success("Race updated successfully");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to update race");
      },
    });

  const result = selectedRace?.results.find(
    (x) => x.athlete_id === selectedAthlete,
  );

  const raceOptions =
    races?.map((x) => ({
      label: `${x.gender} ${x.discipline} ${x.event} ${x.round} ${x.date}`,
      value: x.event_id,
    })) ?? [];

  raceOptions.splice(0, 0, {
    label: "New event",
    value: "new",
  });

  if (race && !raceOptions.find((x) => x.value === race.event_id)) {
    raceOptions.push({
      label: `${race.gender} ${race.discipline} ${race.event} ${race.round} ${race.date}`,
      value: race.event_id,
    });
  }
  const onSave = () => {
    if (!selectedRace || !videoSummary?.competition?.id) return;

    upsertRace({
      id: selectedRace.event_id === "new" ? undefined : selectedRace.event_id,
      videoId,
      competitionId: videoSummary.competition.id,
      date: selectedRace.date ?? selectedRace.date,
      round: selectedRace.round ?? selectedRace.round,
      gender: selectedRace.gender ?? selectedRace.gender,
      discipline: selectedRace.discipline as SnowDiscipline,
      event: selectedRace.event ?? selectedRace.event,
    });
  };

  return (
    <div>
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full bg-white text-black/60">
            Edit Race
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Race</DialogTitle>
            <DialogDescription>
              Competition: {videoSummary?.competition?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <Selector
              options={raceOptions}
              className="bg-white"
              value={selectedRace?.event_id}
              onValueChange={(value) => {
                const race = races?.find((x) => x.event_id === value);
                if (race) {
                  setSelectedRace(race);
                  setSelectedAthlete(race.results[0]?.athlete_id);
                } else {
                  setSelectedRace(initialValues);
                }
              }}
            />
            <Card className="grid gap-2.5 text-smallLabel">
              <div className="flex items-center gap-3">
                <EditableSelector
                  label="Event"
                  options={Object.values(SnowEvent).map((x) => ({
                    label: x,
                    value: x,
                  }))}
                  value={selectedRace.event}
                  onSelect={(v) =>
                    setSelectedRace((prev) => ({
                      ...prev,
                      event: v as SnowEvent,
                    }))
                  }
                />
                {selectedRace.event === SnowEvent.SLOPESTYLE && (
                  <EditSlopeFeature raceId={selectedRace?.event_id ?? ""} />
                )}
              </div>
              <EditableSelector
                label="Discipline"
                options={Object.values(SnowDiscipline).map((x) => ({
                  label: x,
                  value: x,
                }))}
                value={selectedRace.discipline}
                onSelect={(value) =>
                  setSelectedRace((prev) => ({ ...prev, discipline: value }))
                }
              />
              <Input
                label="Round"
                value={selectedRace.round}
                onChange={(e) =>
                  setSelectedRace((prev) => ({
                    ...prev,
                    round: e.target.value,
                  }))
                }
              />
              <EditableSelector
                label="Gender"
                options={Object.values(Gender).map((x) => ({
                  label: x,
                  value: x,
                }))}
                value={selectedRace.gender}
                onSelect={(value) =>
                  setSelectedRace((prev) => ({
                    ...prev,
                    gender: value as Gender,
                  }))
                }
              />
              <Input
                label="Date"
                type="date"
                value={selectedRace.date}
                onChange={(e) =>
                  setSelectedRace((prev) => ({ ...prev, date: e.target.value }))
                }
              />
            </Card>
            <Card className="flex flex-col gap-3">
              <AthleteSelect athleteOptions={athleteOptions} disableDelete />
              <Input label="Placing" disabled value={result?.placing ?? ""} />
              <Input label="Score" disabled value={result?.score ?? ""} />
            </Card>
            <div className="flex gap-2">
              <Button
                disabled={isPendingUpdateVideo}
                onClick={onSave}
                loading={isPendingUpdateVideo}
              >
                Save
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
