import { type InferSelectModel, relations } from "drizzle-orm";
import {
  boolean,
  float,
  int,
  mysqlEnum,
  unique,
  varchar,
} from "drizzle-orm/mysql-core";
import { createTable } from "./schema";
import {
  sprintingDistances,
  sprintingRaceTypes,
  sprintingRounds,
} from "~/lib/enums/sprint";
import { genders } from "~/lib/enums/enums";

export const sprintingRaces = createTable("sprinting_races", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  videoId: varchar("video_id", { length: 255 }).unique().notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  eventGroup: mysqlEnum("event_group", sprintingRaceTypes).notNull(),
  distance: mysqlEnum("distance", sprintingDistances).notNull(),
  round: mysqlEnum("round", sprintingRounds).notNull(),
  position: int("position").notNull(),
  time: varchar("time", { length: 100 }).notNull(),
  track: varchar("track", { length: 100 }).notNull(),
  gender: mysqlEnum("gender", genders).notNull(),
  date: varchar("date", { length: 100 }).notNull(),
  wind: float("wind").notNull(),
  strideFreq: float("stride_freq").notNull(),
});

export type SprintingRace = InferSelectModel<typeof sprintingRaces>;

export const sprintingRacesRelations = relations(
  sprintingRaces,
  ({ many }) => ({
    tags: many(sprintingTags),
  }),
);

export const sprintingTags = createTable(
  "sprinting_tags",
  {
    id: varchar("id", { length: 255 })
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    sprintingRaceId: varchar("sprinting_race_id", { length: 255 }).notNull(),
    athleteId: varchar("athlete_id", { length: 255 }).notNull(),
    tag: varchar("tag", { length: 100 }).notNull(),
    userId: varchar("user_id", { length: 255 }).notNull(),
    frame: int("frame").notNull(),
    isDeleted: boolean("is_deleted").notNull().default(false),
  },
  (table) => ({
    // Add a unique constraint on sprintingRaceId and tag
    tagUnique: unique().on(table.sprintingRaceId, table.tag),
  }),
);

export type SprintingTag = InferSelectModel<typeof sprintingTags>;

export const sprintingTagsRelations = relations(sprintingTags, ({ one }) => ({
  race: one(sprintingRaces, {
    fields: [sprintingTags.sprintingRaceId],
    references: [sprintingRaces.id],
  }),
}));
