"use client";
import { useState, useEffect } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Card } from "~/components/Card";
import { HotkeyButton } from "~/components/HotkeyButton";
import { Input } from "~/components/ui/input";
import { useStore } from "~/hooks/store";
import { useSprintingTags } from "~/hooks/useSprintingTags";
import { SprintTag, sprintTags } from "~/lib/enums/sprint";
import { cn } from "~/lib/utils";

// Helper function to check if a tag is a custom distance (numeric value)
const isCustomDistance = (tag: string): boolean => {
  return !isNaN(Number(tag));
};

export const SprintTagsUI = () => {
  const currentFrame = useStore((state) => state.currentFrame);
  const sprintingRace = useStore((state) => state.sprintingRace);

  const { distances, start, custom, movement } = sprintTags;

  const [editingValues, setEditingValues] = useState<Record<string, string>>(
    {},
  );

  const [customDistanceValue, setCustomDistanceValue] = useState<string>("");

  // Add a key to force re-render when tags change
  const [, forceUpdate] = useState<number>(0);

  const handleInputChange = (tagValue: string, value: string) => {
    setEditingValues((prev) => ({
      ...prev,
      [tagValue]: value,
    }));
  };

  // Use the functions from the useSprintingTags hook
  const { onAddTag, onDeleteTag, tags } = useSprintingTags();

  // Force re-render when tags change to ensure UI is updated
  useEffect(() => {
    forceUpdate((prev) => prev + 1);
  }, [tags]);

  // Add hotkey handlers for each key phase
  // Distance markers
  useHotkeys("1", () => sprintingRace && onAddTag("10m"));
  useHotkeys("2", () => sprintingRace && onAddTag("20m"));
  useHotkeys("3", () => sprintingRace && onAddTag("30m"));
  useHotkeys("4", () => sprintingRace && onAddTag("40m"));
  useHotkeys("5", () => sprintingRace && onAddTag("50m"));
  useHotkeys("6", () => sprintingRace && onAddTag("60m"));
  useHotkeys("7", () => sprintingRace && onAddTag("70m"));
  useHotkeys("8", () => sprintingRace && onAddTag("80m"));
  useHotkeys("9", () => sprintingRace && onAddTag("90m"));
  useHotkeys("0", () => sprintingRace && onAddTag("100m"));

  // Start
  useHotkeys("q", () => sprintingRace && onAddTag("Start"));

  // Custom - we don't use hotkeys for custom distance since it needs a value
  useHotkeys("y", () => sprintingRace && onAddTag("Stride Count"));

  // Movement
  useHotkeys("w", () => sprintingRace && onAddTag("Foot Contact"));
  useHotkeys("e", () => sprintingRace && onAddTag("Touchdown"));
  useHotkeys("r", () => sprintingRace && onAddTag("Toe Off"));

  return (
    <>
      <Card className="gap-2.5 text-smallLabel">
        <p className="uppercase text-black/60">Start</p>
        <div className="flex flex-col gap-2.5">
          {start.map((tagOption) => {
            if (!tagOption?.value) return null;

            const existingTag = tags?.tags.find(
              (t) =>
                t.tag === (tagOption.value as string) &&
                t.raceId === sprintingRace?.id &&
                !t.isDeleted,
            );
            const isSelected = !!existingTag && !existingTag.isDeleted;
            const displayValue =
              editingValues[tagOption.value] ??
              (existingTag && !existingTag.isDeleted
                ? existingTag.frame.toString()
                : "");

            return (
              <div
                key={tagOption.value}
                className="flex w-full items-start justify-between"
                id={
                  existingTag && !existingTag.isDeleted
                    ? existingTag.id
                    : `tag-${tagOption.value}`
                }
              >
                <div className="flex flex-col">
                  <p
                    className={`text-smallLabel ${isSelected ? "font-bold text-black" : "text-gray"}`}
                  >
                    {tagOption.value}
                  </p>
                </div>
                <div className="flex items-start gap-[5px]">
                  <Input
                    type="number"
                    className="h-5"
                    value={displayValue}
                    onChange={(e) =>
                      handleInputChange(tagOption.value, e.target.value)
                    }
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (
                        value &&
                        (!existingTag || existingTag.frame.toString() !== value)
                      ) {
                        onAddTag(tagOption.value, +value);
                      }
                    }}
                    placeholder={
                      existingTag ? `Frame: ${existingTag.frame}` : "Add..."
                    }
                    title={
                      existingTag
                        ? `Edit frame for ${tagOption.value}`
                        : `Add tag ${tagOption.value} at specified frame`
                    }
                  />
                  <HotkeyButton
                    tag={tagOption}
                    onClick={() => onAddTag(tagOption.value)}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      <Card className="gap-2.5 text-smallLabel">
        <p className="uppercase text-black/60">Custom</p>
        <div className="flex flex-col gap-2.5">
          {custom.map((tagOption) => {
            if (!tagOption?.value) return null;

            if (tagOption.value === SprintTag.CustomDistance) {
              const customDistanceTags =
                tags?.tags.filter(
                  (t) =>
                    isCustomDistance(t.tag) &&
                    t.raceId === sprintingRace?.id &&
                    !t.isDeleted,
                ) ?? [];

              return (
                <div key={tagOption.value} className="flex flex-col gap-2.5">
                  <div className="flex w-full items-start justify-between">
                    <div className="flex flex-col">
                      <p className="text-black/60">{tagOption.value}</p>
                    </div>
                    <div className="flex items-start gap-[5px]">
                      <Input
                        type="number"
                        className="h-5"
                        value={customDistanceValue}
                        onChange={(e) => setCustomDistanceValue(e.target.value)}
                        onBlur={(e) => {
                          const value = e.target.value;
                          if (value) {
                            // Check if there's an existing custom distance tag at the current frame
                            const existingTagAtCurrentFrame =
                              customDistanceTags.find(
                                (tag) => tag.frame === currentFrame,
                              );

                            if (existingTagAtCurrentFrame) {
                              // Upsert: Update the existing tag with the new value
                              onAddTag(
                                value,
                                currentFrame,
                                Number(existingTagAtCurrentFrame.id),
                              );
                            } else {
                              // Insert: Create a new tag
                              onAddTag(value);
                            }
                            setCustomDistanceValue("");
                          }
                        }}
                        placeholder="Add..."
                        title="Enter a custom distance value (e.g., 75)"
                      />
                      <div className="flex gap-1">
                        <button
                          className={cn(
                            "flex h-5 items-center justify-center rounded-[2.5px] px-[5px] py-[2.5px]",
                            tagOption.className,
                          )}
                          onClick={() => {
                            if (customDistanceValue) {
                              // Check if there's an existing custom distance tag at the current frame
                              const existingTagAtCurrentFrame =
                                customDistanceTags.find(
                                  (tag) => tag.frame === currentFrame,
                                );

                              if (existingTagAtCurrentFrame) {
                                // Upsert: Update the existing tag with the new value
                                onAddTag(
                                  customDistanceValue,
                                  currentFrame,
                                  Number(existingTagAtCurrentFrame.id),
                                );
                              } else {
                                // Insert: Create a new tag
                                onAddTag(customDistanceValue);
                              }
                              setCustomDistanceValue("");
                            }
                          }}
                        >
                          Add
                        </button>
                      </div>
                    </div>
                  </div>

                  {customDistanceTags.length > 0 && (
                    <div className="mt-2">
                      <p className="mb-1 text-smallLabel text-gray">
                        Existing custom distances:
                      </p>
                      {customDistanceTags.map((tag) => {
                        return (
                          <div
                            key={tag.id}
                            className="mb-1 flex w-full items-start justify-between pl-4"
                            id={tag.id}
                          >
                            <div className="flex flex-col">
                              <p className="text-smallLabel text-black">
                                {tag.tag}m at frame {tag.frame}
                              </p>
                            </div>
                            <div className="flex items-start gap-[5px]">
                              <button
                                className="text-red-500 hover:text-red-700"
                                onClick={() => onDeleteTag(tag.id)}
                                title="Delete this custom distance tag"
                              >
                                ✕
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            }

            const existingTag = tags?.tags.find(
              (t) =>
                t.tag === (tagOption.value as string) &&
                t.raceId === sprintingRace?.id &&
                !t.isDeleted,
            );
            const isSelected = !!existingTag && !existingTag.isDeleted;
            const displayValue =
              editingValues[tagOption.value] ??
              (existingTag && !existingTag.isDeleted
                ? existingTag.frame.toString()
                : "");

            return (
              <div
                key={tagOption.value}
                className="flex w-full items-start justify-between"
                id={
                  existingTag && !existingTag.isDeleted
                    ? existingTag.id
                    : `tag-${tagOption.value}`
                }
              >
                <div className="flex flex-col">
                  <p
                    className={`text-smallLabel ${isSelected ? "font-bold text-black" : "text-gray"}`}
                  >
                    {tagOption.value}
                  </p>
                </div>
                <div className="flex items-start gap-[5px]">
                  <Input
                    type="number"
                    className={cn("h-5")}
                    value={displayValue}
                    onChange={(e) =>
                      handleInputChange(tagOption.value, e.target.value)
                    }
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (
                        value &&
                        (!existingTag || existingTag.frame.toString() !== value)
                      ) {
                        onAddTag(tagOption.value, +value);
                      }
                    }}
                    placeholder={
                      existingTag ? `Frame: ${existingTag.frame}` : "Add..."
                    }
                    title={
                      existingTag
                        ? `Edit frame for ${tagOption.value}`
                        : `Add tag ${tagOption.value} at specified frame`
                    }
                  />
                  <div className="flex gap-1">
                    <HotkeyButton
                      tag={tagOption}
                      onClick={() => onAddTag(tagOption.value)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      <Card className="gap-2.5 text-smallLabel">
        <p className="uppercase text-black/60">Movement</p>
        <div className="flex flex-col gap-2.5">
          {movement.map((tagOption) => {
            if (!tagOption?.value) return null;

            const existingTag = tags?.tags.find(
              (t) =>
                t.tag === (tagOption.value as string) &&
                t.raceId === sprintingRace?.id &&
                !t.isDeleted,
            );
            const isSelected = !!existingTag && existingTag.isDeleted;
            const displayValue =
              editingValues[tagOption.value] ??
              (existingTag && !existingTag.isDeleted
                ? existingTag.frame.toString()
                : "");

            return (
              <div
                key={tagOption.value}
                className="flex w-full items-start justify-between"
                id={
                  existingTag && !existingTag.isDeleted
                    ? existingTag.id
                    : `tag-${tagOption.value}`
                }
              >
                <div className="flex flex-col">
                  <p
                    className={`text-smallLabel ${isSelected ? "font-bold text-black" : "text-gray"}`}
                  >
                    {tagOption.value}
                  </p>
                </div>
                <div className="flex items-start gap-[5px]">
                  <Input
                    type="number"
                    className={cn("h-5")}
                    value={displayValue}
                    onChange={(e) =>
                      handleInputChange(tagOption.value, e.target.value)
                    }
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (
                        value &&
                        (!existingTag || existingTag.frame.toString() !== value)
                      ) {
                        onAddTag(tagOption.value, +value);
                      }
                    }}
                    placeholder={
                      existingTag ? `Frame: ${existingTag.frame}` : "Add..."
                    }
                    title={
                      existingTag
                        ? `Edit frame for ${tagOption.value}`
                        : `Add tag ${tagOption.value} at specified frame`
                    }
                  />
                  <div className="flex gap-1">
                    <HotkeyButton
                      tag={tagOption}
                      onClick={() => onAddTag(tagOption.value)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      <Card className="gap-2.5 text-smallLabel">
        <p className="uppercase text-black/60">Distance Markers</p>
        <div className="flex flex-col gap-2.5">
          {distances.map((tagOption) => {
            if (!tagOption?.value) return null;

            const existingTag = tags?.tags.find(
              (t) =>
                t.tag === (tagOption.value as string) &&
                t.raceId === sprintingRace?.id &&
                !t.isDeleted,
            );
            const isSelected = !!existingTag;
            const displayValue =
              editingValues[tagOption.value] ??
              (existingTag ? existingTag.frame.toString() : "");

            return (
              <div
                key={tagOption.value}
                className="flex w-full items-start justify-between"
                id={existingTag ? existingTag.id : `tag-${tagOption.value}`}
              >
                <div className="flex flex-col">
                  <p
                    className={`text-smallLabel ${isSelected ? "font-bold text-black" : "text-gray"}`}
                  >
                    {tagOption.value}
                  </p>
                </div>
                <div className="flex items-start gap-[5px]">
                  <Input
                    type="number"
                    className={cn("h-5")}
                    value={displayValue}
                    onChange={(e) =>
                      handleInputChange(tagOption.value, e.target.value)
                    }
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (
                        value &&
                        (!existingTag || existingTag.frame.toString() !== value)
                      ) {
                        onAddTag(tagOption.value, +value);
                      }
                    }}
                    placeholder={
                      existingTag ? `Frame: ${existingTag.frame}` : "Add..."
                    }
                    title={
                      existingTag
                        ? `Edit frame for ${tagOption.value}`
                        : `Add tag ${tagOption.value} at specified frame`
                    }
                  />
                  <div className="flex gap-1">
                    <HotkeyButton
                      tag={tagOption}
                      onClick={() => onAddTag(tagOption.value)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    </>
  );
};
